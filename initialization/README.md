# Инициализация данных

Модульная система инициализации данных для телеграм-бота.

## Структура

```
initialization/
├── __init__.py                 # Пакет инициализации
├── init_data.py               # Главный файл (60 строк)
├── courses_subjects.py        # Курсы и предметы (120 строк)
├── groups_users.py           # Группы и пользователи (150 строк)
├── lessons_homework.py       # Уроки и ДЗ (250 строк)
├── test_data.py             # Тестовые данные (300 строк)
├── admin_roles.py           # Роли админов (100 строк)
├── student_assignments.py   # Привязка к курсам (40 строк)
├── month_tests.py          # Тесты месяца (50 строк)
├── update_points.py        # Обновление баллов (40 строк)
└── README.md               # Документация
```

## Запуск

```bash
# Из корня проекта
python -m initialization.init_data

# Или напрямую
cd initialization
python init_data.py
```

## Модули

### 1. `courses_subjects.py`
- Создание курсов (ЕНТ, IT)
- Создание предметов (Математика, Python, и т.д.)
- Создание микротем для каждого предмета
- Привязка предметов к курсам

### 2. `groups_users.py`
- Создание групп по предметам
- Создание пользователей всех ролей
- Привязка пользователей к группам

### 3. `lessons_homework.py`
- Создание уроков по предметам
- Создание домашних заданий
- Создание вопросов и вариантов ответов

### 4. `test_data.py`
- Генерация тестовых результатов ДЗ
- Создание отличных результатов для админа
- Симуляция разных уровней успеха

### 5. `admin_roles.py`
- Добавление всех ролей для админов
- Создание профилей студента, куратора, преподавателя, менеджера

### 6. `student_assignments.py`
- Автоматическая привязка студентов к курсам
- На основе предметов в группах студента

### 7. `month_tests.py`
- Создание тестовых тестов месяца
- По разным предметам и месяцам

### 8. `update_points.py`
- Обновление баллов всех студентов
- Пересчет уровней на основе результатов

## Преимущества новой структуры

1. **Читаемость**: Каждый файл < 300 строк
2. **Модульность**: Логическое разделение функций
3. **Поддержка**: Легко найти и изменить нужную часть
4. **Тестирование**: Можно тестировать модули отдельно
5. **Переиспользование**: Модули можно использовать независимо

## Размеры файлов

- **Старый файл**: 2123 строки
- **Новая структура**: 8 файлов по 40-300 строк
- **Главный файл**: всего 60 строк

Это соответствует лучшим практикам Python разработки!
