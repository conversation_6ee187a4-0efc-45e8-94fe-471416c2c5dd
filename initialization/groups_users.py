"""
Создание групп и пользователей
"""
from database import (
    GroupRepository, UserRepository, StudentRepository, CuratorRepository, 
    TeacherRepository, ManagerRepository
)


async def create_groups_and_users(created_subjects):
    """Создание групп и пользователей"""
    try:
        # Создание групп
        print("👥 Создание групп...")
        groups_data = [
            # ЕНТ группы
            {"name": "М-1", "subject": "Математика"},
            {"name": "М-2", "subject": "Математика"},
            {"name": "Ф-1", "subject": "Физика"},
            {"name": "ИК-1", "subject": "История Казахстана"},
            {"name": "Х-1", "subject": "Химия"},
            {"name": "Б-1", "subject": "Биология"},
            # IT группы
            {"name": "PY-1", "subject": "Python"},
            {"name": "PY-2", "subject": "Python"},
            {"name": "JS-1", "subject": "JavaScript"},
            {"name": "JAVA-1", "subject": "Java"},
        ]

        created_groups = {}
        for group_data in groups_data:
            subject_name = group_data["subject"]
            if subject_name in created_subjects:
                group = await GroupRepository.create(
                    name=group_data["name"],
                    subject_id=created_subjects[subject_name].id
                )
                created_groups[group.name] = group
                print(f"   ✅ Группа '{group.name}' создана для предмета '{subject_name}' (ID: {group.id})")

        # Создание пользователей
        print("👤 Создание пользователей...")
        
        # Админ (получит все роли)
        admin_user = await UserRepository.create(
            telegram_id=955518340,
            name="Андрей Климов",
            role="admin"
        )
        print(f"   ✅ Админ '{admin_user.name}' создан (ID: {admin_user.id})")

        # Менеджеры
        managers_data = [
            {"telegram_id": 111222333, "name": "Алия Сейтова"},
            {"telegram_id": 444555666, "name": "Данияр Жумабеков"},
        ]

        for manager_data in managers_data:
            user = await UserRepository.create(
                telegram_id=manager_data["telegram_id"],
                name=manager_data["name"],
                role="manager"
            )
            manager = await ManagerRepository.create(user_id=user.id)
            print(f"   ✅ Менеджер '{user.name}' создан (ID: {user.id}, Manager ID: {manager.id})")

        # Кураторы
        curators_data = [
            {"telegram_id": 777888999, "name": "Айгерим Касымова", "groups": ["М-1", "М-2"]},
            {"telegram_id": 123456789, "name": "Ерлан Нурланов", "groups": ["Ф-1", "ИК-1"]},
        ]

        for curator_data in curators_data:
            user = await UserRepository.create(
                telegram_id=curator_data["telegram_id"],
                name=curator_data["name"],
                role="curator"
            )
            # Создаем куратора без привязки к конкретному курсу/предмету
            curator = await CuratorRepository.create(user_id=user.id)
            
            # Привязываем к группам
            for group_name in curator_data["groups"]:
                if group_name in created_groups:
                    await CuratorRepository.add_curator_to_group(curator.id, created_groups[group_name].id)
                    print(f"   ✅ Куратор '{user.name}' добавлен в группу '{group_name}'")
            
            print(f"   ✅ Куратор '{user.name}' создан (ID: {user.id}, Curator ID: {curator.id})")

        # Преподаватели
        teachers_data = [
            {"telegram_id": 987654321, "name": "Асель Токтарова", "subject": "Python", "groups": ["PY-1"]},
            {"telegram_id": 555666777, "name": "Максат Ибрагимов", "subject": "JavaScript", "groups": ["JS-1"]},
        ]

        for teacher_data in teachers_data:
            user = await UserRepository.create(
                telegram_id=teacher_data["telegram_id"],
                name=teacher_data["name"],
                role="teacher"
            )
            
            subject_name = teacher_data["subject"]
            if subject_name in created_subjects:
                teacher = await TeacherRepository.create(
                    user_id=user.id,
                    subject_id=created_subjects[subject_name].id
                )
                
                # Привязываем к группам
                for group_name in teacher_data["groups"]:
                    if group_name in created_groups:
                        await TeacherRepository.add_teacher_to_group(teacher.id, created_groups[group_name].id)
                        print(f"   ✅ Преподаватель '{user.name}' добавлен в группу '{group_name}'")
                
                print(f"   ✅ Преподаватель '{user.name}' создан (ID: {user.id}, Teacher ID: {teacher.id})")

        # Студенты
        students_data = [
            {"telegram_id": 333444555, "name": "Муханбетжан Олжас", "groups": ["PY-1"], "tariff": "premium"},
            {"telegram_id": 666777888, "name": "Аружан Ахметова", "groups": ["М-1"], "tariff": "standard"},
            {"telegram_id": 999000111, "name": "Бекзат Сериков", "groups": ["PY-2"], "tariff": "premium"},
            {"telegram_id": 222333444, "name": "Динара Жанибекова", "groups": ["JS-1"], "tariff": "standard"},
            {"telegram_id": 888999000, "name": "Ерасыл Мухамедов", "groups": ["М-2"], "tariff": "premium"},
        ]

        for student_data in students_data:
            user = await UserRepository.create(
                telegram_id=student_data["telegram_id"],
                name=student_data["name"],
                role="student"
            )
            
            student = await StudentRepository.create(
                user_id=user.id,
                tariff=student_data["tariff"]
            )
            
            # Привязываем к группам
            group_ids = []
            for group_name in student_data["groups"]:
                if group_name in created_groups:
                    group_ids.append(created_groups[group_name].id)
            
            if group_ids:
                await StudentRepository.set_groups(student.id, group_ids)
                group_names = ", ".join(student_data["groups"])
                print(f"   ✅ Студент '{user.name}' создан и добавлен в группы: {group_names} (ID: {user.id}, Student ID: {student.id})")

        print(f"👥 Создание групп и пользователей завершено!")

    except Exception as e:
        print(f"❌ Ошибка при создании групп и пользователей: {e}")
